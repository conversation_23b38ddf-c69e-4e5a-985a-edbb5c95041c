# showAgentDiff 方法实现总结

## 概述
基于 `rollbackToCheckpoint` 方法的时间戳查找逻辑，实现了 `showAgentDiff` 方法，用于显示指定文件在历史版本与当前版本之间的差异。

## 实现思路

### 1. 使用 EntireFileHistoryDialogModel
- 由于 `filepath` 参数已经是具体的文件路径，使用 `EntireFileHistoryDialogModel` 比 `DirectoryHistoryDialogModel` 更简单直接
- 直接针对单个文件进行历史版本查找和内容获取，获取文件的完整历史记录

### 2. 核心实现步骤

#### 步骤1: 获取文件的VirtualFile对象
```kotlin
val virtualFile = LocalFileSystem.getInstance()
    .findFileByPath(UriUtils.uriToFile(filepath).absolutePath)
```

#### 步骤2: 创建EntireFileHistoryDialogModel
```kotlin
val localHistoryImpl = LocalHistoryImpl.getInstanceImpl()
val fileHistoryModel = EntireFileHistoryDialogModel(
    project,
    localHistoryImpl.gateway,
    localHistoryImpl.facade,
    virtualFile
)
```

#### 步骤3: 查找目标时间戳的历史版本
```kotlin
val targetRevisionIndex = LocalHistoryUtil.findClosestRevisionBeforeTimestamp(
    fileHistoryModel,
    timestamp
)
```

#### 步骤4: 获取历史版本内容
```kotlin
val targetRevision = revisions[targetRevisionIndex]
val historicalContent = targetRevision.revision.findEntry("")?.content
```

#### 步骤5: 显示diff对比
- 获取当前文件内容
- 调用 `showHistoricalDiff` 方法显示差异对比窗口

### 3. 辅助方法

#### showHistoricalDiff
- 创建临时文件存储历史版本和当前版本内容
- 使用IntelliJ的DiffManager显示差异对比窗口

#### showHistoricalDiffWindow
- 创建只读的diff查看窗口
- 显示格式化的时间戳信息
- 提供关闭按钮和临时文件清理功能

#### 工具方法
- `getDiffDirectory()`: 获取diff临时文件目录
- `escapeFilepath()`: 转义文件路径用于创建临时文件名

## 主要特性

1. **时间戳精确查找**: 复用 `rollbackToCheckpoint` 的时间戳查找逻辑
2. **异常处理**: 直接抛出异常，不使用Toast提示，让调用方处理错误
3. **UI友好**: 使用IntelliJ原生的diff显示组件
4. **资源管理**: 自动清理临时文件
5. **只读查看**: diff窗口为只读模式，不影响文件内容

## 关键修正

### 使用正确的历史模型
- ✅ **EntireFileHistoryDialogModel**: 专门用于单个文件的完整历史记录
- ❌ ~~FileHistoryDialogModel~~: 不是最佳选择
- ❌ ~~DirectoryHistoryDialogModel~~: 用于目录级别的历史记录

### 正确的内容获取方式
```kotlin
// 对于EntireFileHistoryDialogModel，使用空字符串作为entry路径
val content = targetRevision.revision.findEntry("")?.content
```

### 异常处理策略
```kotlin
// 文件不存在时直接抛异常
if (virtualFile == null) {
    throw IllegalArgumentException("File not found: $filepath")
}

// 没有找到历史版本时抛异常
if (targetRevisionIndex < 0) {
    throw LocalHistoryException("No suitable revision found before timestamp: $timestamp")
}
```

## 使用场景
- 查看AI代理在特定时间点对文件的修改
- 对比文件的历史版本与当前版本
- 调试和审查代码变更历史

## 依赖的类和方法
- `LocalHistoryImpl`: IntelliJ本地历史记录实现
- `EntireFileHistoryDialogModel`: 完整文件历史对话框模型
- `LocalHistoryUtil.findClosestRevisionBeforeTimestamp`: 时间戳查找工具方法
- `DiffManager`: IntelliJ差异显示管理器
- `UriUtils`: URI工具类
